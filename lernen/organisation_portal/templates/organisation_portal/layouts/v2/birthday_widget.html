 <div class="col-12 col-lg-3 d-flex">
    <div class="card flex-fill w-100 pl-0 pr-0">
      <div class="card-header">
        <h5 class="card-title mb-0" id="new-admission-graph-header-text">Student Birthdays</h5>
    </div>
      <nav>
        <div class="nav nav-tabs" id="nav-tab" role="tablist">
          <a class="nav-item nav-link active" id="nav-after-enrollment-structure-tab" data-toggle="tab" href="#nav-after-enrollment-structure" role="tab" aria-controls="nav-home" aria-selected="true">Today</a>
          <a class="nav-item nav-link" id="nav-before-registration-structure-tab" data-toggle="tab" href="#before-registration-structure" role="tab" aria-controls="nav-profile" aria-selected="false">Upcoming</a>
        </div>
      </nav>
      <br/>
          <div class="tab-content" id="nav-tabContent">
            <div class="tab-pane fade show active" id="nav-after-enrollment-structure" role="tabpanel" aria-labelledby="nav-after-enrollment-structure-tab">
              <div style="display:block;overflow-y: auto;max-height: 385px;">
              {% for institute_unique_key, institute_stats in institute_details_map.items %}
              {% if institute_stats.today_birthday %}
              <p style="background-color: #e6f8fa;color: #000000;font-size: .9rem;font-weight: 500;" class="pl-4 pb-2 pt-2 pr-4 mb-0">{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</p>
              <div class="d-flex">
                <div class="w-100">
                  <div class="mb-0">
                    {% for student in institute_stats.today_birthday %}
                    <div class="card-border" style="border-right: 0px; border-left: 0px;cursor:pointer;" onclick="homePage.generateBirthdayCertificate(this);">
                      <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                        <div class="mt-3 ml-0 mr-2 mb-3 row">
                          <div class="col-3" style="padding-right: 0px;">
                            <div class="stat">
                              <div style="text-align: center;">{{ student.studentBasicInfo.name|extract_initials }}</div>
                            </div>
                          </div>
                          <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                            <p class="mb-0" style="font-size:14px;">{{student.studentBasicInfo.name}} ({{student.studentBasicInfo.admissionNumber}})</p>
                            <p class="logo-color mb-0">{{student.studentBasicInfo.dateOfBirth|print_date_with_month_text}}</p>
                          </div>
                          <div class="col-3">
                            <p class="mb-0 pt-3" style="float:right">{{student.studentAcademicSessionInfoResponse.standard.standardName}}</p>
                          </div>
                        </div>
                    </div>
                    {% endfor %}
                    </div>
                  </div>
                </div>
              {% endif %}
              {% endfor %}
              </div>
          </div>
          <div class="tab-pane fade" id="before-registration-structure" role="tabpanel" aria-labelledby="nav-before-registration-structure-tab">
            <div style="display:block;overflow-y: auto;max-height: 385px;">
            {% for institute_unique_key, institute_stats in institute_details_map.items %}
            {% if institute_stats.upcoming_birthday %}
            <p style="background-color: #e6f8fa;color: #000000;font-size: .9rem;font-weight: 500;" class="pl-4 pb-2 pt-2 pr-4 mb-0">{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</p>
            <div class="d-flex">
              <div class="w-100">
                <div class="mb-0">
                    {% for student in institute_stats.upcoming_birthday %}
                    <div class="card-border" style="border-right: 0px; border-left: 0px;cursor:pointer;" onclick="homePage.generateBirthdayCertificate(this);">
                      <p class="student-info" style="display:none;">{{student|jsonstr}}</p>
                        <div class="mt-3 ml-0 mr-2 mb-3 row">
                          <div class="col-3" style="padding-right: 0px;">
                            <div class="stat">
                              <div style="text-align: center;">{{ student.studentBasicInfo.name|extract_initials }}</div>
                            </div>
                          </div>
                          <div class="col-6 pb-0 mb-0 pt-1 pl-0">
                            <p class="mb-0" style="font-size:14px;">{{student.studentBasicInfo.name}} ({{student.studentBasicInfo.admissionNumber}})</p>
                            <p class="logo-color mb-0">{{student.studentBasicInfo.dateOfBirth|print_date_with_month_text}}</p>
                          </div>
                          <div class="col-3">
                            <p class="mb-0 pt-3" style="float:right">{{student.studentAcademicSessionInfoResponse.standard.standardName}}</p>
                          </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
          </div>
          {% endif %}
          {% endfor %}
          </div>
        </div>
      </div>
    </div>
    </div>